import 'package:get/get.dart';
import '../screens/auth_wrapper.dart';
import '../screens/login_screen.dart';
import '../screens/dashboard_screen.dart';
import '../screens/customers_list_screen.dart';
import '../screens/order_list_screen.dart';
import '../screens/add_order_screen.dart';
import '../screens/add_customer_screen.dart';
import '../controllers/login_controller.dart';
import '../controllers/dashboard_controller.dart';
import '../controllers/customers_list_controller.dart';
import '../controllers/order_list_controller.dart';
import '../controllers/add_order_controller.dart';
import '../controllers/add_customer_controller.dart';
import '../middleware/auth_middleware.dart';
import 'app_routes.dart';

class AppPages {
  static const initial = AppRoutes.authWrapper;

  static final routes = [
    GetPage(
      name: AppRoutes.authWrapper,
      page: () => const AuthWrapper(),
    ),
    GetPage(
      name: AppRoutes.login,
      page: () => const LoginScreen(),
      binding: BindingsBuilder(() {
        Get.lazyPut<LoginController>(() => LoginController());
      }),
    ),
    GetPage(
      name: AppRoutes.dashboard,
      page: () => const DashboardScreen(),
      binding: BindingsBuilder(() {
        Get.lazyPut<DashboardController>(() => DashboardController());
      }),
      middlewares: [AuthGuard()],
    ),
    GetPage(
      name: AppRoutes.customersList,
      page: () => const CustomersListScreen(),
      binding: BindingsBuilder(() {
        Get.lazyPut<CustomersListController>(() => CustomersListController());
      }),
      middlewares: [AuthGuard()],
    ),
    GetPage(
      name: AppRoutes.orderList,
      page: () => const OrderListScreen(),
      binding: BindingsBuilder(() {
        Get.lazyPut<OrderListController>(() => OrderListController());
      }),
      middlewares: [AuthGuard()],
    ),
    GetPage(
      name: AppRoutes.addOrder,
      page: () => const AddOrderScreen(),
      binding: BindingsBuilder(() {
        Get.lazyPut<AddOrderController>(() => AddOrderController());
      }),
      middlewares: [AuthGuard()],
    ),
    GetPage(
      name: AppRoutes.addCustomer,
      page: () => const AddCustomerScreen(),
      binding: BindingsBuilder(() {
        Get.lazyPut<AddCustomerController>(() => AddCustomerController());
      }),
      middlewares: [AuthGuard()],
    ),
    // Add more routes as needed
  ];
}
